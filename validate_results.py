"""
验证流水线输出结果的质量
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def validate_pipeline_results(output_dir='outputs'):
    """验证流水线输出结果"""
    print("=" * 60)
    print("流水线结果验证")
    print("=" * 60)
    
    # 1. 加载数据
    print("1. 加载数据文件...")
    try:
        processed_df = pd.read_csv(f'{output_dir}/processed.csv')
        factors_df = pd.read_csv(f'{output_dir}/factors.csv')
        weights_df = pd.read_csv(f'{output_dir}/factor_weights.csv')
        print(f"  ✓ processed.csv: {processed_df.shape}")
        print(f"  ✓ factors.csv: {factors_df.shape}")
        print(f"  ✓ factor_weights.csv: {weights_df.shape}")
    except Exception as e:
        print(f"  ✗ 加载失败: {e}")
        return
    
    # 2. 验证因子范围
    print("\n2. 验证因子数值范围 [0,1]...")
    for col in factors_df.columns:
        min_val = factors_df[col].min()
        max_val = factors_df[col].max()
        if min_val >= 0 and max_val <= 1:
            print(f"  ✓ {col}: [{min_val:.4f}, {max_val:.4f}]")
        else:
            print(f"  ✗ {col}: [{min_val:.4f}, {max_val:.4f}] - 超出范围!")
    
    # 3. 验证权重和
    print("\n3. 验证因子权重和...")
    for factor in weights_df['factor'].unique():
        factor_weights = weights_df[weights_df['factor'] == factor]['weight']
        weight_sum = factor_weights.sum()
        if abs(weight_sum - 1.0) < 1e-6:
            print(f"  ✓ {factor}: {weight_sum:.6f}")
        else:
            print(f"  ✗ {factor}: {weight_sum:.6f} - 权重和不为1!")
    
    # 4. 检查缺失值
    print("\n4. 检查缺失值...")
    missing_processed = processed_df.isnull().sum().sum()
    missing_factors = factors_df.isnull().sum().sum()
    print(f"  processed.csv 缺失值: {missing_processed}")
    print(f"  factors.csv 缺失值: {missing_factors}")
    
    # 5. 因子相关性分析
    print("\n5. 因子相关性分析...")
    correlation_matrix = factors_df.corr()
    print("因子相关性矩阵:")
    print(correlation_matrix.round(3))
    
    # 6. 目标变量分布
    print("\n6. 目标变量分布...")
    if 'y' in processed_df.columns:
        y_counts = processed_df['y'].value_counts().sort_index()
        print("目标变量分布:")
        for val, count in y_counts.items():
            print(f"  y={val}: {count} ({count/len(processed_df)*100:.1f}%)")
    
    # 7. 关键特征统计
    print("\n7. 关键特征统计...")
    key_features = ['duration_min', 'road_complex_cnt', 'is_peak', 'is_weekend']
    for feature in key_features:
        if feature in processed_df.columns:
            stats = processed_df[feature].describe()
            print(f"  {feature}: mean={stats['mean']:.3f}, std={stats['std']:.3f}")
    
    # 8. 权重分析
    print("\n8. 各因子权重分布...")
    for factor in weights_df['factor'].unique():
        factor_data = weights_df[weights_df['factor'] == factor]
        top_features = factor_data.nlargest(3, 'weight')
        print(f"  {factor} 前3个重要特征:")
        for _, row in top_features.iterrows():
            print(f"    {row['feature']}: {row['weight']:.4f}")
    
    print("\n" + "=" * 60)
    print("验证完成!")
    print("=" * 60)

if __name__ == "__main__":
    validate_pipeline_results()
