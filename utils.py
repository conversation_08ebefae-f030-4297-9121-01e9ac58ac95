"""
公共工具函数
"""
import numpy as np
import pandas as pd
from typing import Dict, Any

# 风向映射表
WIND_DIRECTION_MAP = {
    'N': 0, 'NNE': 22.5, 'NE': 45, 'ENE': 67.5,
    'E': 90, 'ESE': 112.5, 'SE': 135, 'SSE': 157.5,
    'S': 180, 'SSW': 202.5, 'SW': 225, 'WSW': 247.5,
    'W': 270, 'WNW': 292.5, 'NW': 315, 'NNW': 337.5
}

def map_wind_direction(wind_dir: str) -> float:
    """将风向字符串映射为角度"""
    if pd.isna(wind_dir):
        return np.nan
    wind_dir_clean = str(wind_dir).strip().upper()
    return WIND_DIRECTION_MAP.get(wind_dir_clean, np.nan)

def wind_to_sincos(wind_deg: float) -> tuple:
    """将风向角度转换为sin/cos分量"""
    if pd.isna(wind_deg):
        # 缺失时按0度处理
        return 0.0, 1.0
    rad = np.radians(wind_deg)
    return np.sin(rad), np.cos(rad)

def circular_encode(values: pd.Series, period: float) -> tuple:
    """循环特征编码"""
    rad = 2 * np.pi * values / period
    return np.sin(rad), np.cos(rad)

def zscore_by_group(df: pd.DataFrame, value_col: str, group_col: str) -> pd.Series:
    """按组计算z-score"""
    def zscore_group(group):
        mean_val = group.mean()
        std_val = group.std()
        if std_val == 0 or pd.isna(std_val):
            return group * 0  # 返回0
        return (group - mean_val) / std_val
    
    return df.groupby(group_col)[value_col].transform(zscore_group).fillna(0)

def safe_float_convert(series: pd.Series) -> pd.Series:
    """安全转换为浮点数"""
    return pd.to_numeric(series, errors='coerce')

def safe_datetime_convert(series: pd.Series) -> pd.Series:
    """安全转换为datetime"""
    return pd.to_datetime(series, errors='coerce')

def standardize_boolean(series: pd.Series) -> pd.Series:
    """标准化布尔值"""
    series_clean = series.astype(str).str.lower().str.strip()
    return series_clean.isin(['true', 't', '1', 'yes', 'y']).astype(int)

def extract_weather_conditions(weather_str: str) -> Dict[str, int]:
    """提取天气条件指示变量"""
    if pd.isna(weather_str):
        weather_str = ""
    weather_lower = str(weather_str).lower()
    
    conditions = {
        'wc_clear': 0,
        'wc_rain': 0, 
        'wc_snow': 0,
        'wc_fog': 0,
        'wc_storm': 0
    }
    
    # clear/fair但不含partly
    if any(word in weather_lower for word in ['clear', 'fair']) and 'partly' not in weather_lower:
        conditions['wc_clear'] = 1
        
    # rain相关
    if any(word in weather_lower for word in ['rain', 'drizzle', 'shower', 'storm']):
        conditions['wc_rain'] = 1
        
    # snow相关  
    if any(word in weather_lower for word in ['snow', 'sleet', 'ice', 'blizzard', 'wintry']):
        conditions['wc_snow'] = 1
        
    # fog相关
    if any(word in weather_lower for word in ['fog', 'haze', 'smoke', 'mist']):
        conditions['wc_fog'] = 1
        
    # storm相关
    if any(word in weather_lower for word in ['thunder', 'storm', 'squall']):
        conditions['wc_storm'] = 1
        
    return conditions

def extract_description_keywords(desc_str: str) -> Dict[str, int]:
    """提取描述关键词指示变量"""
    import re
    
    if pd.isna(desc_str):
        desc_str = ""
    desc_lower = str(desc_str).lower()
    
    keywords = {
        'kw_ramp': 0,
        'kw_closed': 0,
        'kw_multiveh': 0, 
        'kw_overturned': 0
    }
    
    # ramp
    if re.search(r'\bramp\b', desc_lower):
        keywords['kw_ramp'] = 1
        
    # closed
    if re.search(r'\bclosed\b', desc_lower):
        keywords['kw_closed'] = 1
        
    # multi-vehicle
    if re.search(r'multi[-\s]?vehicle|multiple vehicles', desc_lower):
        keywords['kw_multiveh'] = 1
        
    # overturned
    if re.search(r'\boverturn|jackknif', desc_lower):
        keywords['kw_overturned'] = 1
        
    return keywords

def clip_duration(start_time: pd.Series, end_time: pd.Series) -> pd.Series:
    """计算并截断持续时间"""
    duration = (end_time - start_time).dt.total_seconds() / 60
    return duration.clip(0, 480).fillna(0)

def clip_weather_lag(start_time: pd.Series, weather_time: pd.Series) -> pd.Series:
    """计算并截断天气滞后时间"""
    lag = (start_time - weather_time).dt.total_seconds() / 60
    return lag.clip(-180, 180)
