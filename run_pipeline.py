"""
交通事故数据预处理流水线主入口
"""
import argparse
import os
import pandas as pd
import numpy as np
from pathlib import Path

from preprocess import load_and_clean
from target_encoding import attach_oof_encodings
from factors import build_factors

def main():
    parser = argparse.ArgumentParser(description='交通事故数据预处理流水线')
    parser.add_argument('--input_csv', type=str, required=True, help='输入CSV文件路径')
    parser.add_argument('--output_dir', type=str, required=True, help='输出目录')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--folds', type=int, default=5, help='目标编码折数')
    parser.add_argument('--smooth', type=float, default=20.0, help='目标编码平滑系数')
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print("=" * 60)
    print("交通事故数据预处理流水线")
    print("=" * 60)
    print(f"输入文件: {args.input_csv}")
    print(f"输出目录: {args.output_dir}")
    print(f"随机种子: {args.seed}")
    print(f"目标编码折数: {args.folds}")
    print(f"平滑系数: {args.smooth}")
    print("=" * 60)
    
    # 步骤1: 数据清洗和特征工程
    print("\n步骤1: 数据清洗和特征工程")
    print("-" * 40)
    df_processed = load_and_clean(args.input_csv, seed=args.seed)
    
    # 检查目标变量
    if 'y' not in df_processed.columns:
        raise ValueError("目标变量 'y' 未找到，请检查数据处理流程")
    
    y = df_processed['y'].values
    print(f"目标变量分布: {np.bincount(y.astype(int))}")
    
    # 步骤2: 折外目标编码
    print("\n步骤2: 折外目标编码")
    print("-" * 40)
    df_with_encoding = attach_oof_encodings(
        df_processed, 
        y, 
        folds=args.folds, 
        smooth=args.smooth, 
        seed=args.seed
    )
    
    # 步骤3: 语义因子构建
    print("\n步骤3: 语义因子构建")
    print("-" * 40)
    factors_df, weights_df = build_factors(df_with_encoding, y, seed=args.seed)
    
    # 步骤4: 保存结果
    print("\n步骤4: 保存结果")
    print("-" * 40)
    
    # 保存处理后的数据
    processed_path = output_dir / 'processed.parquet'
    try:
        df_with_encoding.to_parquet(processed_path, index=False)
        print(f"已保存: {processed_path}")
    except ImportError:
        # 如果没有pyarrow，使用CSV格式
        processed_path_csv = output_dir / 'processed.csv'
        df_with_encoding.to_csv(processed_path_csv, index=False)
        print(f"已保存 (CSV格式): {processed_path_csv}")

    # 保存因子数据
    factors_path = output_dir / 'factors.parquet'
    try:
        factors_df.to_parquet(factors_path, index=False)
        print(f"已保存: {factors_path}")
    except ImportError:
        # 如果没有pyarrow，使用CSV格式
        factors_path_csv = output_dir / 'factors.csv'
        factors_df.to_csv(factors_path_csv, index=False)
        print(f"已保存 (CSV格式): {factors_path_csv}")
    
    # 保存权重数据
    weights_path = output_dir / 'factor_weights.csv'
    weights_df.to_csv(weights_path, index=False)
    print(f"已保存: {weights_path}")
    
    # 输出摘要信息
    print("\n" + "=" * 60)
    print("处理完成摘要")
    print("=" * 60)
    print(f"处理后数据形状: {df_with_encoding.shape}")
    print(f"因子数据形状: {factors_df.shape}")
    print(f"权重记录数: {len(weights_df)}")
    
    print("\n因子统计:")
    for col in factors_df.columns:
        print(f"  {col}: min={factors_df[col].min():.4f}, max={factors_df[col].max():.4f}, mean={factors_df[col].mean():.4f}")
    
    print("\n权重验证:")
    for factor in weights_df['factor'].unique():
        factor_weights = weights_df[weights_df['factor'] == factor]['weight']
        weight_sum = factor_weights.sum()
        print(f"  {factor}: 权重和 = {weight_sum:.6f}")
    
    print("\n流水线执行完成!")

if __name__ == "__main__":
    main()
