"""
折外目标编码模块
"""
import pandas as pd
import numpy as np
from sklearn.model_selection import KFold
from typing import List

def oof_target_encode(
    series: pd.Series, 
    y: np.ndarray, 
    folds: int = 5, 
    smooth: float = 20.0, 
    seed: int = 42
) -> np.ndarray:
    """
    K折折外目标编码
    
    Args:
        series: 要编码的分类变量
        y: 目标变量
        folds: 折数
        smooth: 平滑系数
        seed: 随机种子
        
    Returns:
        编码后的数值数组
    """
    # 全局均值
    global_mean = np.mean(y)
    
    # 初始化结果数组
    encoded = np.full(len(series), global_mean)
    
    # K折交叉验证
    kf = KFold(n_splits=folds, shuffle=True, random_state=seed)
    
    for train_idx, val_idx in kf.split(series):
        # 训练集统计
        train_series = series.iloc[train_idx]
        train_y = y[train_idx]
        
        # 计算每个类别的统计量
        stats = pd.DataFrame({
            'category': train_series,
            'target': train_y
        }).groupby('category')['target'].agg(['count', 'mean']).reset_index()
        
        # 平滑目标编码
        stats['encoded'] = (stats['count'] * stats['mean'] + smooth * global_mean) / (stats['count'] + smooth)
        
        # 创建映射字典
        encoding_map = dict(zip(stats['category'], stats['encoded']))
        
        # 对验证集进行编码
        val_series = series.iloc[val_idx]
        for i, val in enumerate(val_series):
            encoded[val_idx[i]] = encoding_map.get(val, global_mean)
    
    return encoded

def attach_oof_encodings(
    df: pd.DataFrame, 
    y: np.ndarray, 
    folds: int, 
    smooth: float, 
    seed: int
) -> pd.DataFrame:
    """
    对指定的高基数字段进行OOF目标编码
    
    Args:
        df: 数据框
        y: 目标变量
        folds: 折数
        smooth: 平滑系数
        seed: 随机种子
        
    Returns:
        添加了编码列的数据框
    """
    # 需要编码的列
    encode_cols = ['grid_id', 'Street', 'City', 'Zipcode']
    
    df_encoded = df.copy()
    
    for col in encode_cols:
        if col in df.columns:
            print(f"正在对 {col} 进行OOF目标编码...")
            
            # 执行编码
            encoded_values = oof_target_encode(
                df[col].fillna('MISSING'), 
                y, 
                folds=folds, 
                smooth=smooth, 
                seed=seed
            )
            
            # 添加编码列
            new_col = f"{col}_te"
            df_encoded[new_col] = encoded_values
            
            print(f"  {col} -> {new_col}: 完成")
    
    return df_encoded
