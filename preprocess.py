"""
数据预处理模块：字段清洗、数值化、特征衍生
"""
import pandas as pd
import numpy as np
from typing import Dict, Any
from utils import (
    map_wind_direction, wind_to_sincos, circular_encode, zscore_by_group,
    safe_float_convert, safe_datetime_convert, standardize_boolean,
    extract_weather_conditions, extract_description_keywords,
    clip_duration, clip_weather_lag
)

def load_and_clean(input_csv: str, seed: int = 42) -> pd.DataFrame:
    """
    读取CSV并进行完整的数据清洗和特征工程
    
    Args:
        input_csv: 输入CSV文件路径
        seed: 随机种子
        
    Returns:
        清洗后的DataFrame，包含所有衍生特征
    """
    print("正在读取数据...")
    # 读取为字符串类型
    df = pd.read_csv(input_csv, dtype=str)
    print(f"原始数据形状: {df.shape}")
    
    # 去除空行和ID重复
    df = df.dropna(how='all')
    df = df.drop_duplicates(subset=['ID'], keep='first')
    print(f"去重后数据形状: {df.shape}")
    
    # 1. 时间解析与时序衍生
    print("处理时间特征...")
    df = _process_time_features(df)
    
    # 2. 经纬度与网格
    print("处理地理特征...")
    df = _process_geo_features(df)
    
    # 3. 数值列转换
    print("处理数值特征...")
    df = _process_numeric_features(df)
    
    # 4. 风向处理
    print("处理风向特征...")
    df = _process_wind_features(df)
    
    # 5. 昼夜特征
    print("处理昼夜特征...")
    df = _process_daynight_features(df)
    
    # 6. 道路设施特征
    print("处理道路设施特征...")
    df = _process_road_features(df)
    
    # 7. 天气条件特征
    print("处理天气条件特征...")
    df = _process_weather_conditions(df)
    
    # 8. 描述关键词特征
    print("处理描述关键词特征...")
    df = _process_description_keywords(df)
    
    # 9. 月内z-score
    print("计算月内z-score...")
    df = _process_monthly_zscore(df)
    
    # 10. 目标变量映射
    print("处理目标变量...")
    df = _process_target_variable(df)
    
    print(f"最终数据形状: {df.shape}")
    return df

def _process_time_features(df: pd.DataFrame) -> pd.DataFrame:
    """处理时间相关特征"""
    # 转换时间列
    time_cols = ['Start_Time', 'End_Time', 'Weather_Timestamp']
    for col in time_cols:
        if col in df.columns:
            df[col] = safe_datetime_convert(df[col])
    
    # 持续时间
    if 'Start_Time' in df.columns and 'End_Time' in df.columns:
        df['duration_min'] = clip_duration(df['Start_Time'], df['End_Time'])
    
    # 时间衍生特征
    if 'Start_Time' in df.columns:
        df['hour'] = df['Start_Time'].dt.hour
        df['dow'] = df['Start_Time'].dt.weekday  
        df['month'] = df['Start_Time'].dt.month
        
        # 循环特征
        df['hour_sin'], df['hour_cos'] = circular_encode(df['hour'].fillna(0), 24)
        df['dow_sin'], df['dow_cos'] = circular_encode(df['dow'].fillna(0), 7)
        
        # 高峰时段和周末
        df['is_peak'] = df['hour'].apply(lambda x: 1 if x in list(range(7, 10)) + list(range(16, 20)) else 0)
        df['is_weekend'] = (df['dow'] >= 5).astype(int)
    
    # 天气观测滞后
    if 'Start_Time' in df.columns and 'Weather_Timestamp' in df.columns:
        df['wx_lag_min'] = clip_weather_lag(df['Start_Time'], df['Weather_Timestamp'])
        # 用中位数填充缺失值
        median_lag = df['wx_lag_min'].median()
        df['wx_lag_min'] = df['wx_lag_min'].fillna(median_lag)
    
    return df

def _process_geo_features(df: pd.DataFrame) -> pd.DataFrame:
    """处理地理特征"""
    # 转换经纬度
    if 'Start_Lat' in df.columns:
        df['Start_Lat'] = safe_float_convert(df['Start_Lat'])
    if 'Start_Lng' in df.columns:
        df['Start_Lng'] = safe_float_convert(df['Start_Lng'])
    
    # 构造网格ID
    if 'Start_Lat' in df.columns and 'Start_Lng' in df.columns:
        df['grid_id'] = (df['Start_Lat'].round(2).astype(str) + '_' + 
                        df['Start_Lng'].round(2).astype(str))
    
    return df

def _process_numeric_features(df: pd.DataFrame) -> pd.DataFrame:
    """处理数值特征"""
    numeric_cols = [
        'Temperature(F)', 'Wind_Chill(F)', 'Humidity(%)', 'Pressure(in)',
        'Visibility(mi)', 'Wind_Speed(mph)', 'Precipitation(in)', 'Distance(mi)'
    ]
    
    for col in numeric_cols:
        if col in df.columns:
            df[col] = safe_float_convert(df[col])
            # 用中位数填充缺失值
            median_val = df[col].median()
            df[col] = df[col].fillna(median_val)
    
    return df

def _process_wind_features(df: pd.DataFrame) -> pd.DataFrame:
    """处理风向特征"""
    if 'Wind_Direction' in df.columns:
        # 映射风向到角度
        df['wind_deg'] = df['Wind_Direction'].apply(map_wind_direction)
        
        # 转换为sin/cos
        wind_sincos = df['wind_deg'].apply(wind_to_sincos)
        df['wind_sin'] = [x[0] for x in wind_sincos]
        df['wind_cos'] = [x[1] for x in wind_sincos]
    
    return df

def _process_daynight_features(df: pd.DataFrame) -> pd.DataFrame:
    """处理昼夜特征"""
    daynight_cols = ['Sunrise_Sunset', 'Civil_Twilight', 'Nautical_Twilight', 'Astronomical_Twilight']
    
    for col in daynight_cols:
        if col in df.columns:
            new_col = f"{col}_bin"
            df[new_col] = df[col].apply(lambda x: 1 if str(x).lower() == 'day' else 0)
    
    return df

def _process_road_features(df: pd.DataFrame) -> pd.DataFrame:
    """处理道路设施特征"""
    road_cols = [
        'Amenity', 'Bump', 'Crossing', 'Give_Way', 'Junction', 'No_Exit',
        'Railway', 'Roundabout', 'Station', 'Stop', 'Traffic_Calming',
        'Traffic_Signal', 'Turning_Loop'
    ]
    
    for col in road_cols:
        if col in df.columns:
            df[col] = standardize_boolean(df[col])
    
    # 计算道路复杂度
    road_bool_cols = [col for col in road_cols if col in df.columns]
    if road_bool_cols:
        df['road_complex_cnt'] = df[road_bool_cols].sum(axis=1)
    
    return df

def _process_weather_conditions(df: pd.DataFrame) -> pd.DataFrame:
    """处理天气条件特征"""
    if 'Weather_Condition' in df.columns:
        weather_features = df['Weather_Condition'].apply(extract_weather_conditions)
        weather_df = pd.DataFrame(weather_features.tolist())
        df = pd.concat([df, weather_df], axis=1)
    
    return df

def _process_description_keywords(df: pd.DataFrame) -> pd.DataFrame:
    """处理描述关键词特征"""
    if 'Description' in df.columns:
        desc_features = df['Description'].apply(extract_description_keywords)
        desc_df = pd.DataFrame(desc_features.tolist())
        df = pd.concat([df, desc_df], axis=1)
    
    return df

def _process_monthly_zscore(df: pd.DataFrame) -> pd.DataFrame:
    """计算月内z-score"""
    zscore_cols = ['Temperature(F)', 'Pressure(in)', 'Visibility(mi)', 'Wind_Speed(mph)']
    
    if 'month' in df.columns:
        for col in zscore_cols:
            if col in df.columns:
                new_col = f"{col}_zm"
                df[new_col] = zscore_by_group(df, col, 'month')
    
    return df

def _process_target_variable(df: pd.DataFrame) -> pd.DataFrame:
    """处理目标变量"""
    if 'Severity' in df.columns:
        # 转换为数值
        df['Severity'] = safe_float_convert(df['Severity'])
        
        # 映射 {1→0, 2→0, 3→1, 4→2}
        severity_map = {1: 0, 2: 0, 3: 1, 4: 2}
        df['y'] = df['Severity'].map(severity_map)
        
        # 移除无法映射的记录
        before_count = len(df)
        df = df.dropna(subset=['y'])
        after_count = len(df)
        print(f"目标变量映射: 移除了 {before_count - after_count} 条无效记录")
    
    return df
