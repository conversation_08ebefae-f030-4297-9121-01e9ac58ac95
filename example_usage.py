"""
使用示例：如何使用生成的因子数据
"""
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score

def example_usage():
    """演示如何使用生成的因子数据进行建模"""
    print("=" * 60)
    print("因子数据使用示例")
    print("=" * 60)
    
    # 1. 加载因子数据
    print("1. 加载因子数据...")
    factors_df = pd.read_csv('outputs/factors.csv')
    processed_df = pd.read_csv('outputs/processed.csv')
    
    # 提取特征和标签
    X = factors_df.values  # 5个因子作为特征
    y = processed_df['y'].values  # 目标变量
    
    print(f"特征矩阵形状: {X.shape}")
    print(f"标签向量形状: {y.shape}")
    print(f"因子列名: {list(factors_df.columns)}")
    
    # 2. 数据分割
    print("\n2. 数据分割...")
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    print(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
    
    # 3. 训练简单的随机森林模型
    print("\n3. 训练随机森林模型...")
    rf = RandomForestClassifier(n_estimators=100, random_state=42)
    rf.fit(X_train, y_train)
    
    # 4. 预测和评估
    print("\n4. 模型评估...")
    y_pred = rf.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    print(f"准确率: {accuracy:.4f}")
    
    print("\n分类报告:")
    print(classification_report(y_test, y_pred))
    
    # 5. 特征重要性分析
    print("\n5. 因子重要性分析...")
    feature_importance = rf.feature_importances_
    factor_names = factors_df.columns
    
    importance_df = pd.DataFrame({
        'factor': factor_names,
        'importance': feature_importance
    }).sort_values('importance', ascending=False)
    
    print("因子重要性排序:")
    for _, row in importance_df.iterrows():
        print(f"  {row['factor']}: {row['importance']:.4f}")
    
    # 6. 因子统计分析
    print("\n6. 因子统计分析...")
    print("各因子的统计信息:")
    print(factors_df.describe().round(4))
    
    # 7. 按目标变量分组的因子均值
    print("\n7. 不同严重程度下的因子均值:")
    factors_with_target = factors_df.copy()
    factors_with_target['y'] = y
    
    group_means = factors_with_target.groupby('y')[factor_names].mean()
    print(group_means.round(4))
    
    print("\n" + "=" * 60)
    print("示例完成!")
    print("=" * 60)

if __name__ == "__main__":
    example_usage()
