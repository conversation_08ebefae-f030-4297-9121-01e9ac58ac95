"""
语义因子构建模块
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import MinMaxScaler
from typing import Tuple, Dict, List

# 因子定义
FACTOR_DEFINITIONS = {
    'F_vis': [
        'Visibility(mi)_zm', 'Precipitation(in)', 'wc_fog', 'wc_rain', 'wc_snow',
        'Sunrise_Sunset_bin', 'Nautical_Twilight_bin', 'Wind_Speed(mph)_zm', 'wx_lag_min'
    ],
    'F_road': [
        'road_complex_cnt', 'Junction', 'Crossing', 'Traffic_Signal', 'Stop',
        'Roundabout', 'Bump', 'Traffic_Calming', 'kw_ramp', 'kw_closed',
        'kw_multiveh', 'kw_overturned'
    ],
    'F_expo': [
        'grid_id_te', 'Street_te', 'City_te', 'Zipcode_te'
    ],
    'F_time': [
        'is_peak', 'is_weekend', 'hour_sin', 'hour_cos', 'dow_sin', 'dow_cos'
    ],
    'F_wx': [
        'Pressure(in)_zm', 'Temperature(F)_zm', 'Wind_Speed(mph)_zm',
        'wc_storm', 'wc_rain', 'wc_snow', 'Precipitation(in)'
    ]
}

def build_factors(
    df: pd.DataFrame, 
    y: np.ndarray, 
    seed: int = 42
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    构建语义因子和权重
    
    Args:
        df: 包含所需特征的数据框
        y: 目标变量
        seed: 随机种子
        
    Returns:
        factors_df: 因子得分数据框 (5列)
        weights_df: 权重数据框 (factor, feature, weight)
    """
    print("开始构建语义因子...")
    
    # 创建二元标签
    y_bin = (y > 0).astype(int)
    
    factors_dict = {}
    weights_list = []
    
    for factor_name, feature_list in FACTOR_DEFINITIONS.items():
        print(f"处理因子: {factor_name}")
        
        # 获取可用特征
        available_features = [f for f in feature_list if f in df.columns]
        if not available_features:
            print(f"  警告: {factor_name} 没有可用特征，跳过")
            factors_dict[factor_name] = np.zeros(len(df))
            continue
            
        print(f"  可用特征: {available_features}")
        
        # 提取特征矩阵
        X = df[available_features].copy()
        
        # 缺失值填充（用中位数）
        for col in X.columns:
            if X[col].dtype in ['float64', 'int64']:
                median_val = X[col].median()
                X[col] = X[col].fillna(median_val)
            else:
                X[col] = X[col].fillna(0)
        
        # 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        X_scaled_df = pd.DataFrame(X_scaled, columns=available_features)
        
        # 训练L1逻辑回归
        try:
            lr = LogisticRegression(
                penalty='l1', 
                solver='liblinear', 
                max_iter=200, 
                random_state=seed,
                C=1.0
            )
            lr.fit(X_scaled, y_bin)
            
            # 获取权重
            coef = lr.coef_[0]
            weights = np.abs(coef)
            
            # 非负化并归一化
            weights = np.maximum(weights, 0)
            if weights.sum() > 0:
                weights = weights / weights.sum()
            else:
                # 如果所有权重都是0，使用均匀权重
                weights = np.ones(len(weights)) / len(weights)
                
        except Exception as e:
            print(f"  警告: {factor_name} 逻辑回归失败: {e}")
            # 使用均匀权重
            weights = np.ones(len(available_features)) / len(available_features)
        
        # 记录权重
        for feature, weight in zip(available_features, weights):
            weights_list.append({
                'factor': factor_name,
                'feature': feature,
                'weight': weight
            })
        
        # 计算因子原始得分
        z_raw = X_scaled_df.values @ weights
        
        # Min-Max归一化到[0,1]
        min_max_scaler = MinMaxScaler()
        z_normalized = min_max_scaler.fit_transform(z_raw.reshape(-1, 1)).flatten()
        
        factors_dict[factor_name] = z_normalized
        
        print(f"  {factor_name}: 完成 (权重和={weights.sum():.6f})")
    
    # 构建因子数据框
    factor_order = ['F_vis', 'F_road', 'F_expo', 'F_time', 'F_wx']
    factors_df = pd.DataFrame({
        factor: factors_dict.get(factor, np.zeros(len(df))) 
        for factor in factor_order
    })
    
    # 构建权重数据框
    weights_df = pd.DataFrame(weights_list)
    
    print("语义因子构建完成!")
    print(f"因子数据框形状: {factors_df.shape}")
    print(f"权重数据框形状: {weights_df.shape}")
    
    # 验证权重和
    for factor in factor_order:
        if factor in weights_df['factor'].values:
            factor_weights = weights_df[weights_df['factor'] == factor]['weight']
            weight_sum = factor_weights.sum()
            print(f"{factor} 权重和: {weight_sum:.6f}")
    
    return factors_df, weights_df
